const mysql = require("mysql2/promise");
const mysqlPool = require("../config/mysqldb");
const sql = require("mssql");
const dbConfig = require("../config/db");
const fs = require("fs");
const path = require("path");
const { DateTime } = require("luxon");

// 獲取出勤紀錄
const getAttendanceRecords = async (req, res) => {
  const { sn, year_month } = req.query;

  if (!sn || !year_month) {
    return res.status(400).json({ message: "缺少必要參數" });
  }

  try {
    // 從 year_month 解析年月
    const [year, month] = year_month.split("-");

    // 查詢用戶的人資內部編號
    const [userRows] = await mysqlPool.execute(
      "SELECT id FROM users WHERE sn = ?",
      [sn]
    );

    if (userRows.length === 0) {
      return res.status(404).json({ message: "找不到該員工" });
    }

    const userId = userRows[0].id;

    // 查詢該用戶在指定年月的出勤紀錄
    // 直接返回原始時間欄位，讓前端處理格式化
    const [records] = await mysqlPool.execute(
      `SELECT 
        att_date, 
        first_in,
        first_out,
        second_in,
        second_out,
        late_time, 
        first_in_f, 
        first_out_f, 
        second_in_f, 
        second_out_f 
      FROM att_records 
      WHERE user_id = ? 
      AND YEAR(att_date) = ? 
      AND MONTH(att_date) = ? 
      ORDER BY att_date ASC`,
      [userId, year, month]
    );

    // 添加調試資訊
    // console.log("查詢到的出勤紀錄:", records.length, "筆");
    // if (records.length > 0) {
    //   console.log("第一筆紀錄範例:", records[0]);
    // }

    res.json(records);
  } catch (error) {
    console.error("獲取出勤紀錄失敗:", error);
    res.status(500).json({ message: "獲取出勤紀錄失敗", error: error.message });
  }
};

// 獲取用戶資訊
const getUserInfo = async (req, res) => {
  const { sn } = req.query;

  if (!sn) {
    return res.status(400).json({ message: "缺少員工編號參數" });
  }

  try {
    // 查詢用戶資訊
    const [userRows] = await mysqlPool.execute(
      "SELECT id, sn, name FROM users WHERE sn = ?",
      [sn]
    );

    if (userRows.length === 0) {
      return res.status(404).json({ message: "找不到該員工" });
    }

    res.json(userRows[0]);
  } catch (error) {
    console.error("獲取用戶資訊失敗:", error);
    res.status(500).json({ message: "獲取用戶資訊失敗", error: error.message });
  }
};

// 獲取門市資料
const getBranchs = async (req, res) => {
  try {
    const pool = await sql.connect(dbConfig);

    const result = await pool.request().query(`
      SELECT 
        Cod_cust AS Cod_Cust, 
        Cod_name AS Cod_Name, 
        Cod_group, 
        Invest, 
        District, 
        Sts, 
        Ip, 
        Rtime, 
        AttendanceIP, 
        Comment_Url, 
        Comment_Rating, 
        Comment_Count, 
        Comment_At, 
        AttendanceSync
      FROM Branch 
      WHERE Cod_group<>'A' AND Sts='1' AND AttendanceIP IS NOT NULL AND AttendanceIP <> ''
    `);

    // 確保返回的是陣列
    res.json(result.recordset);
  } catch (error) {
    console.error("獲取門市資料失敗:", error);
    res.status(500).json({ error: "伺服器錯誤，無法獲取門市資料" });
  }
};

// 下載出勤紀錄 (從設備下載到資料庫)
const downloadAttendance = async (req, res) => {
  const { Cod_cust, AttendanceIP } = req.body;

  if (!Cod_cust || !AttendanceIP) {
    return res.status(400).json({
      status: "error",
      error: "缺少必要參數：Cod_cust 或 AttendanceIP",
    });
  }

  try {
    // 這裡需要實現從考勤設備下載資料的邏輯
    // 由於沒有具體的設備 SDK，這裡提供一個模擬實現
    console.log(`正在從設備 ${AttendanceIP} 下載考勤資料...`);

    // 模擬設備連接和資料下載
    // 實際實現需要根據具體的考勤設備 API 來調整
    const deviceCount = Math.floor(Math.random() * 100); // 模擬設備記錄數
    const writeCount = Math.floor(deviceCount * 0.8); // 模擬寫入資料庫的記錄數

    // 更新門市的最後同步時間
    const pool = await sql.connect(dbConfig);
    await pool.request().input("cod_cust", sql.VarChar, Cod_cust).query(`
        UPDATE Branch
        SET AttendanceSync = GETUTCDATE()
        WHERE Cod_cust = @cod_cust
      `);

    res.json({
      status: "success",
      deviceCount: deviceCount,
      count: writeCount,
      message: `成功下載 ${writeCount} 筆考勤資料`,
    });
  } catch (error) {
    console.error("下載考勤資料失敗:", error);
    res.status(500).json({
      status: "error",
      error: "下載考勤資料失敗：" + error.message,
    });
  }
};

// 匯出到 FTP
const exportToFtp = async (req, res) => {
  const {
    host,
    user,
    password,
    remoteDir,
    filename,
    startDate,
    endDate,
    stateMap,
  } = req.body;

  if (!host || !user || !password || !startDate || !endDate) {
    return res.status(400).json({
      status: "error",
      message: "缺少必要的 FTP 參數或日期範圍",
    });
  }

  try {
    // 查詢指定日期範圍的考勤資料
    const [records] = await mysqlPool.execute(
      `
      SELECT
        u.sn as employee_id,
        u.name as employee_name,
        ar.att_date,
        ar.first_in,
        ar.first_out,
        ar.second_in,
        ar.second_out
      FROM att_records ar
      JOIN users u ON ar.user_id = u.id
      WHERE ar.att_date BETWEEN ? AND ?
      ORDER BY ar.att_date, u.sn
    `,
      [startDate, endDate]
    );

    if (records.length === 0) {
      return res.json({
        status: "warning",
        message: "指定日期範圍內沒有考勤資料",
      });
    }

    // 生成匯出文件內容
    let exportContent =
      "員工編號,員工姓名,日期,上班時間,下班時間,午休開始,午休結束\n";

    records.forEach((record) => {
      const formatTime = (time) =>
        time ? DateTime.fromJSDate(time).toFormat("HH:mm") : "";

      exportContent +=
        [
          record.employee_id,
          record.employee_name,
          DateTime.fromJSDate(record.att_date).toFormat("yyyy-MM-dd"),
          formatTime(record.first_in),
          formatTime(record.first_out),
          formatTime(record.second_in),
          formatTime(record.second_out),
        ].join(",") + "\n";
    });

    // 這裡需要實現 FTP 上傳邏輯
    // 由於沒有 FTP 套件，這裡提供模擬實現
    console.log(`模擬上傳到 FTP: ${host}, 用戶: ${user}, 目錄: ${remoteDir}`);
    console.log(`匯出資料筆數: ${records.length}`);

    res.json({
      status: "success",
      message: `成功匯出 ${records.length} 筆資料到 FTP 伺服器`,
    });
  } catch (error) {
    console.error("FTP 匯出失敗:", error);
    res.status(500).json({
      status: "error",
      message: "FTP 匯出失敗：" + error.message,
    });
  }
};

// 測試 FTP 連線
const testFtp = async (req, res) => {
  const { host, user, password } = req.query;

  if (!host || !user || !password) {
    return res.status(400).json({
      status: "error",
      message: "缺少 FTP 連線參數",
    });
  }

  try {
    // 這裡需要實現 FTP 連線測試邏輯
    // 模擬連線測試
    console.log(`測試 FTP 連線: ${host}, 用戶: ${user}`);

    // 模擬連線結果
    const isConnected = Math.random() > 0.2; // 80% 成功率

    if (isConnected) {
      res.json({
        status: "success",
        message: "FTP 連線測試成功",
      });
    } else {
      res.json({
        status: "error",
        message: "FTP 連線測試失敗，請檢查連線參數",
      });
    }
  } catch (error) {
    console.error("FTP 連線測試失敗:", error);
    res.status(500).json({
      status: "error",
      message: "FTP 連線測試失敗：" + error.message,
    });
  }
};

// 刪除歷史考勤紀錄
const deleteAttendance = async (req, res) => {
  const { beforeDate } = req.body;

  if (!beforeDate) {
    return res.status(400).json({
      error: "缺少必要參數：beforeDate",
    });
  }

  try {
    // 刪除指定日期之前的考勤紀錄
    const [result] = await mysqlPool.execute(
      `
      DELETE FROM att_records
      WHERE att_date < ?
    `,
      [beforeDate]
    );

    res.json({
      message: "刪除成功",
      rowsAffected: result.affectedRows,
    });
  } catch (error) {
    console.error("刪除歷史考勤紀錄失敗:", error);
    res.status(500).json({
      error: "刪除失敗：" + error.message,
    });
  }
};

// 批次下載狀態管理
let batchStatus = [];

// 批次下載考勤資料
const batchDownload = async (req, res) => {
  try {
    // 獲取所有有考勤設備的門市
    const pool = await sql.connect(dbConfig);
    const result = await pool.request().query(`
      SELECT Cod_cust, Cod_name, AttendanceIP
      FROM Branch
      WHERE Cod_group<>'A' AND Sts='1' AND AttendanceIP IS NOT NULL AND AttendanceIP <> ''
    `);

    const branches = result.recordset;

    // 初始化批次狀態
    batchStatus = branches.map((branch) => ({
      cod_cust: branch.Cod_cust,
      cod_name: branch.Cod_name,
      attendanceIP: branch.AttendanceIP,
      status: "pending",
      message: "等待處理...",
      count: 0,
    }));

    // 開始批次處理（異步執行）
    processBatchDownload();

    res.json({
      message: "批次下載已啟動",
      totalBranches: branches.length,
    });
  } catch (error) {
    console.error("批次下載啟動失敗:", error);
    res.status(500).json({
      error: "批次下載啟動失敗：" + error.message,
    });
  }
};

// 處理批次下載（異步函數）
const processBatchDownload = async () => {
  for (let i = 0; i < batchStatus.length; i++) {
    const branch = batchStatus[i];

    try {
      batchStatus[i].status = "processing";
      batchStatus[i].message = "正在下載...";

      // 模擬下載過程
      await new Promise((resolve) =>
        setTimeout(resolve, 2000 + Math.random() * 3000)
      );

      // 模擬下載結果
      const success = Math.random() > 0.1; // 90% 成功率

      if (success) {
        const count = Math.floor(Math.random() * 50);
        batchStatus[i].status = "success";
        batchStatus[i].message = `下載成功，共 ${count} 筆資料`;
        batchStatus[i].count = count;

        // 更新門市同步時間
        const pool = await sql.connect(dbConfig);
        await pool.request().input("cod_cust", sql.VarChar, branch.cod_cust)
          .query(`
            UPDATE Branch
            SET AttendanceSync = GETUTCDATE()
            WHERE Cod_cust = @cod_cust
          `);
      } else {
        batchStatus[i].status = "error";
        batchStatus[i].message = "設備連線失敗";
      }
    } catch (error) {
      batchStatus[i].status = "error";
      batchStatus[i].message = "處理失敗：" + error.message;
    }
  }
};

// 獲取批次下載狀態
const getBatchStatus = async (req, res) => {
  res.json(batchStatus);
};

// 下載考勤資料為 TXT 文件
const downloadAttendanceTxt = async (req, res) => {
  const { startDate, endDate, stateMap } = req.body;

  if (!startDate || !endDate) {
    return res.status(400).json({
      error: "缺少必要參數：startDate 或 endDate",
    });
  }

  try {
    // 查詢指定日期範圍的考勤資料
    const [records] = await mysqlPool.execute(
      `
      SELECT
        u.sn as employee_id,
        u.name as employee_name,
        ar.att_date,
        ar.first_in,
        ar.first_out,
        ar.second_in,
        ar.second_out,
        ar.late_time,
        ar.first_in_f,
        ar.first_out_f,
        ar.second_in_f,
        ar.second_out_f
      FROM att_records ar
      JOIN users u ON ar.user_id = u.id
      WHERE ar.att_date BETWEEN ? AND ?
      ORDER BY ar.att_date, u.sn
    `,
      [startDate, endDate]
    );

    if (records.length === 0) {
      return res.status(404).json({
        error: "指定日期範圍內沒有考勤資料",
      });
    }

    // 生成 TXT 文件內容
    let txtContent = "";

    records.forEach((record) => {
      const formatTime = (time) =>
        time ? DateTime.fromJSDate(time).toFormat("HHmm") : "0000";
      const formatDate = DateTime.fromJSDate(record.att_date).toFormat(
        "yyyyMMdd"
      );

      // 根據狀態對照表轉換狀態碼
      const getStateCode = (flag) => {
        if (!stateMap || !flag) return "A";
        return stateMap[flag] || "A";
      };

      // 生成考勤記錄行
      if (record.first_in) {
        txtContent += `${record.employee_id},${formatDate},${formatTime(
          record.first_in
        )},${getStateCode(record.first_in_f)}\n`;
      }
      if (record.first_out) {
        txtContent += `${record.employee_id},${formatDate},${formatTime(
          record.first_out
        )},${getStateCode(record.first_out_f)}\n`;
      }
      if (record.second_in) {
        txtContent += `${record.employee_id},${formatDate},${formatTime(
          record.second_in
        )},${getStateCode(record.second_in_f)}\n`;
      }
      if (record.second_out) {
        txtContent += `${record.employee_id},${formatDate},${formatTime(
          record.second_out
        )},${getStateCode(record.second_out_f)}\n`;
      }
    });

    // 設置響應標頭並返回文件內容
    res.setHeader("Content-Type", "text/plain; charset=utf-8");
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="attendance_${DateTime.now().toFormat(
        "yyyyMMdd"
      )}.txt"`
    );
    res.send(txtContent);
  } catch (error) {
    console.error("下載 TXT 文件失敗:", error);
    res.status(500).json({
      error: "下載失敗：" + error.message,
    });
  }
};

module.exports = {
  getAttendanceRecords,
  getUserInfo,
  getBranchs,
  downloadAttendance,
  exportToFtp,
  testFtp,
  deleteAttendance,
  batchDownload,
  getBatchStatus,
  downloadAttendanceTxt,
};
