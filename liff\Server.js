const express = require("express");
const sql = require("mssql");
const path = require("path");
const { DateTime } = require("luxon");
const multer = require("multer");
const dotenv = require("dotenv");
const { closeAllPools } = require("./services/dbPoolManager");

dotenv.config();

// 設置時區為台北時間
process.env.TZ = "Asia/Taipei";

const dbConfig = require("./config/db"); // ✅ 載入資料庫設定
const authMiddleware = require("./middleware/authMiddleware");
// 引入路由
const pageRoutes = require("./routes/pageRoutes");
const userRoutes = require("./routes/userRoutes");
const depsRoutes = require("./routes/deps");
const branchRoutes = require("./routes/branch");
const formsRoutes = require("./routes/forms");
const formsendRoutes = require("./routes/formsend");
const formsearchRoutes = require("./routes/formsearch");
const salesRoutes = require("./routes/salesRoutes");
const btinRoutes = require("./routes/btinRoutes");
const eventsRoutes = require("./routes/eventsRoutes");
const btimestatRoutes = require("./routes/btimestatRoutes");
const bclassstatRoutes = require("./routes/bclassstatRoutes");
const btastestatRoutes = require("./routes/btastestatRoutes");
const bsummaryRoutes = require("./routes/bsummaryRoutes");
const minvoRoutes = require("./routes/minvoRoutes");
const gcountsRoutes = require("./routes/gcountsRoutes");
const commentsRoutes = require("./routes/commentsRoutes");
const attendanceRoutes = require("./routes/attendanceRoutes");
const orderRoutes = require("./routes/orderRoutes");
const orderinternalRoutes = require("./routes/orderinternalRoutes");
const weborderRoutes = require("./routes/weborderRoutes");
const tablestatusRoutes = require("./routes/tablestatusRoutes");
const o_ticketRoutes = require("./routes/o_ticketRoutes");
const o_tasteRoutes = require("./routes/o_tasteRoutes");
const o_menumRoutes = require("./routes/o_menumRoutes");
const o_setsRoutes = require("./routes/o_setsRoutes");
const o_vclassRoutes = require("./routes/o_vclassRoutes");
const o_kclassRoutes = require("./routes/o_kclassRoutes");
const o_cclassRoutes = require("./routes/o_cclassRoutes");
const o_modeRoutes = require("./routes/o_modeRoutes");
const o_otherpayRoutes = require("./routes/o_otherpayRoutes");
const calendarsetRoutes = require("./routes/calendarsetRoutes");
const systemRoutes = require("./routes/systemRoutes");
const authRoutes = require("./routes/authRoutes");
const checkSqlConnection = require("./controllers/ping");
const payslipRoutes = require("./routes/payslipRoutes");
const leaveRoutes = require("./routes/uleaveRoutes");

// 引入發送通知的函數
const {
  sendPendingNotifications,
  sendPendingBulletinNotifications,
} = require("./controllers/sendEmail");

const app = express();
// 增加 JSON 請求體大小限制為 50MB
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// 🔹 設定 `multer` 存放檔案
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    // 檢查是否有指定上傳路徑
    const uploadPath = req.body.upload_path || "./uploads/";

    // 確保目錄存在
    const fs = require("fs");
    fs.mkdirSync(uploadPath, { recursive: true });

    cb(null, uploadPath); // 使用指定的目錄或預設目錄
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + path.extname(file.originalname)); // ✅ 避免檔名重複
  },
});
const upload = multer({ storage });

// 🔹 讓 `/uploads` 目錄可被存取
app.use("/uploads", express.static(path.join(__dirname, "./uploads")));

// 添加額外的靜態文件服務，確保圖片可以被訪問
app.use(express.static(path.join(__dirname, "./")));

// 允許 CORS（如果前端在不同伺服器時需要）
const cors = require("cors");
// 設定 CORS，允許所有來源，並設定允許的方法和標頭
app.use(
  cors({
    origin: "*", // 允許所有來源，或設定特定來源如 'https://localhost'
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: ["Content-Type", "Authorization"],
  })
);

// 掛載 API 路由
app.use("/api/auth", authRoutes); // 🔹 `/api/auth/login`、`/api/auth/verify-token`
app.use("/api/users", userRoutes); // 🔹 `/api/users`
app.use("/api/forms", formsRoutes); // 🔹 `/api/forms/update-approval-status` /api/forms/get_form_fields
app.use("/api/formsearch", formsearchRoutes); // 🔹 `/api/formsearch/form_seach
app.use("/api/formsend", formsendRoutes); // 🔹 `/api/formsend/submit_ot
app.use("/api/page", pageRoutes); // 🔹 `/api/page/
app.use("/api/deps", depsRoutes); // 🔹 `/api/deps/
app.use("/api/branch", branchRoutes); // 🔹 `/api/branch/
app.use("/api/system", systemRoutes); // 🔹 `/api/system/
app.use("/api/order", orderRoutes); // 🔹 `/api/order/
app.use("/api/minvo", minvoRoutes); // 🔹 `/api/minvo/
app.use("/api/sales", salesRoutes); // 🔹 `/api/sales/
app.use("/api/att", attendanceRoutes); // 🔹 `/api/att/
//app.use("/api/attendance", attendanceRoutes); // 🔹 `/api/attendance/
app.use("/api/comments", commentsRoutes); // 🔹 `/api/comments/
app.use("/api/bsummary", bsummaryRoutes); // 🔹 `/api/bsummary/
app.use("/api/bclassstat", bclassstatRoutes); // 🔹 `/api/bclassstat/
app.use("/api/btimestat", btimestatRoutes); // 🔹 `/api/btimestat/
app.use("/api/tablestatus", tablestatusRoutes); // 🔹 `/api/tablestatus/
app.use("/api/weborder", weborderRoutes); // 🔹 `/api/weborder/
app.use("/api/orderinternal", orderinternalRoutes); // 🔹 `/api/orderinternal/
app.use("/api/onlinetaste", o_tasteRoutes); // 🔹 `/api/onlinetasteRoutes/
app.use("/api/onlinecclass", o_cclassRoutes); // 🔹 `/api/onlinetasteRoutes/
app.use("/api/ovclass", o_vclassRoutes); // 🔹 `/api/onlinetasteRoutes/
app.use("/api/okclass", o_kclassRoutes); // 🔹 `/api/okclass/
app.use("/api/omenum", o_menumRoutes); // 掛載 o_menum API 路由
app.use("/api/omode", o_modeRoutes); // 掛載 o_mode API 路由
app.use("/api/otherpay", o_otherpayRoutes);
app.use("/api/oticket", o_ticketRoutes); // 掛載 o_ticket API 路由
app.use("/api/osets", o_setsRoutes); // 新增 o_sets 路由
app.use("/api/btastestat", btastestatRoutes); // 新增 btastestat 路由
app.use("/api/gcounts", gcountsRoutes); // 新增商品統計路由
app.use("/api/btin", btinRoutes); // 新增公告系統路由
app.use("/api/events", eventsRoutes); // 新增事件系統路由
app.use("/api/calendarset", calendarsetRoutes); // 新增行事曆設定路由
app.use("/api/payslip", payslipRoutes);
app.use("/api/leave", leaveRoutes); // 新增請假系統路由

app.get("/api/ping", async (req, res) => {
  const { cod_cust } = req.query;
  if (!cod_cust)
    return res.status(400).json({ status: "error", message: "缺少 cod_cust" });

  const result = await checkSqlConnection(cod_cust);
  res.json(result);
});

app.post("/api/get_pending_requests", authMiddleware, async (req, res) => {
  const { userid } = req.body; // 接收 `userId` (對應 Approver_id)

  if (!userid) {
    return res.status(400).json({ error: "❌ 缺少 userid" });
  }

  try {
    const pool = await sql.connect(dbConfig);

    // 🔍 只查詢主檔 (不再預先載入 Form_leave)
    const result = await pool
      .request()
      .input("approver_id", sql.VarChar, userid)
      .input("status", sql.VarChar, "waiting").query(`
        SELECT 
          f.Form_id,
          f.userid AS submitter_id,
          u.Name AS submitter,
          f.Created AS submit_date,
          f.Type AS form_type, -- ✅ 動態對應不同單證類型
          fa.Step_number AS step,
          n.Name AS doc_name,
          n.Color AS doc_color
        FROM Forms_approver fa
        JOIN Forms f ON fa.Form_id = f.Form_id
        JOIN Users u ON f.userid = u.id
        JOIN Users su ON fa.approver_id = su.ID
        JOIN Forms_name n ON n.Type = f.Type
        WHERE su.id = @approver_id  AND fa.Status = @status
        ORDER BY f.Created DESC;
      `);

    res.json(result.recordset); // ✅ 回傳主檔資料
  } catch (err) {
    console.error("❌ 查詢待簽核表單失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法查詢待簽核表單" });
  }
});

app.post("/api/get_pending_tasks", async (req, res) => {
  const { userid } = req.body; // 前端傳來的使用者 ID

  if (!userid) {
    return res.status(400).json({ error: "❌ 缺少 userid" });
  }
  try {
    const pool = await sql.connect(dbConfig);
    const userId = userid;
    //2️⃣ 查詢 `Forms_approver` 表，找出 `status = 'waiting'` 的數量
    const pendingResult = await pool
      .request()
      .input("approver_id", sql.VarChar, userId)
      .input("status", sql.VarChar, "waiting")
      .query(
        "SELECT COUNT(*) AS pendingCount FROM Forms_approver WHERE Approver_id = @approver_id AND Status = @status"
      );

    const pendingTasks = pendingResult.recordset[0].pendingCount || 0;
    res.json({ pendingTasks });
  } catch (err) {
    console.error("❌ 查詢待簽核數量失敗:", err);
    res.status(500).json({ error: "❌ 伺服器錯誤，無法查詢待簽核數量" });
  }
});

app.post(
  "/api/upload_attachments",
  upload.array("attachments"),
  async (req, res) => {
    const { form_id, upload_path } = req.body;
    const files = req.files; // ✅ 取得上傳的檔案列表

    if (!form_id) {
      return res.status(400).json({ error: "❌ 缺少 `form_id`，無法儲存附件" });
    }

    if (!files || files.length === 0) {
      return res.status(400).json({ error: "❌ 沒有上傳任何附件" });
    }

    try {
      const pool = await sql.connect(dbConfig);

      for (let i = 0; i < files.length; i++) {
        // 使用指定的上傳路徑，如果沒有則使用預設路徑
        const filePath = upload_path
          ? `/${upload_path}${files[i].filename}`
          : `/uploads/${files[i].filename}`; // ✅ 檔案儲存位置

        await pool
          .request()
          .input("form_id", sql.VarChar, form_id)
          .input("sno", sql.Int, i + 1)
          .input("file_url", sql.NVarChar, filePath)
          .query(
            "INSERT INTO Forms_attachments (Form_id, sno, file_url, Created_at) VALUES (@form_id, @sno, @file_url, GETDATE())"
          );
      }

      res.json({ status: "success", message: "✅ 附件上傳成功！" });
    } catch (err) {
      console.error("❌ 上傳附件失敗:", err);
      res.status(500).json({ error: "❌ 伺服器錯誤，無法儲存附件" });
    }
  }
);
process.on("SIGINT", async () => {
  console.log("\n🛑 伺服器關閉中，釋放連線池...");
  await closeAllPools();
  console.log("🧼 所有連線池已釋放完畢，安全關閉！");
  process.exit();
});

process.on("SIGTERM", async () => {
  console.log("\n🛑 伺服器被終止，釋放連線池...");
  await closeAllPools();
  console.log("🧼 所有連線池已釋放完畢，安全關閉！");
  process.exit();
});

app.use(express.static(path.join(__dirname, "/")));
// 讓所有非 API 的請求回傳 `index.html`
app.get("*", (req, res) => {
  res.sendFile(path.join(__dirname, "/index.html"));
});

// // 定時任務：每小時檢查一次是否有待發送的通知
// const cron = require("node-cron");

// // 每小時執行一次（在每小時的第0分鐘）
// cron.schedule("0 * * * *", async () => {
//   console.log("🕒 執行定時任務: 檢查待發送的表單通知...");
//   try {
//     await sendPendingNotifications();
//   } catch (error) {
//     console.error("❌ 執行定時發送表單通知任務失敗:", error);
//   }
// });

// // 每小時執行一次（在每小時的第30分鐘）
// cron.schedule("30 * * * *", async () => {
//   console.log("🕒 執行定時任務: 檢查待發送的公告通知...");
//   try {
//     await sendPendingBulletinNotifications();
//   } catch (error) {
//     console.error("❌ 執行定時發送公告通知任務失敗:", error);
//   }
// });

// 啟動伺服器
const PORT = process.env.PORT || 5000;
app.listen(PORT, () =>
  console.log(`🚀 Server running on http://localhost:${PORT}`)
);
