const express = require("express");
const {
  getAttendanceRecords,
  getUserInfo,
  getBranchs,
  downloadAttendance,
  exportToFtp,
  testFtp,
  deleteAttendance,
  batchDownload,
  getBatchStatus,
  downloadAttendanceTxt,
} = require("../controllers/attendanceController");

const router = express.Router();
const authMiddleware = require("../middleware/authMiddleware");

// 現有路由
router.get("/records", authMiddleware, getAttendanceRecords);
router.get("/user-info", authMiddleware, getUserInfo);
router.get("/get_branchs", authMiddleware, getBranchs);

// 新增的考勤管理路由
router.post("/download_Attendance", authMiddleware, downloadAttendance);
router.post("/export_Ftp", authMiddleware, exportToFtp);
router.get("/test_Ftp", authMiddleware, testFtp);
router.post("/delete_Attendance", authMiddleware, deleteAttendance);
router.post("/batch_Download", authMiddleware, batchDownload);
router.get("/batch_Status", authMiddleware, getBatchStatus);
router.post("/download_AttendanceTxt", authMiddleware, downloadAttendanceTxt);

module.exports = router;
